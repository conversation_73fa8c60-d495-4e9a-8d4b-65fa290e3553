"use client";

import { Box, Typography, Skeleton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import MedicationIcon from "@mui/icons-material/Medication";

const Speciality = ({ specialities = [], isLoading = false, locationCode }) => {
  const router = useRouter();
  const theme = useTheme();

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "24px", overflow: "hidden" }}>
        {[1, 2, 3].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={300}
            sx={{ 
              width: "100%", 
              minWidth: { xs: "280px", sm: "320px", md: "350px" },
              borderRadius: "12px"
            }}
          />
        ))}
      </Box>
    );
  }

  const handleSpecialityClick = (speciality) => {
    const { seoSlug = "" } = speciality || {};
    if (seoSlug && locationCode) {
      router.push(`/specialities/${locationCode}/${seoSlug}`);
    }
  };

  return (
    <Box>
      <Swiper
        spaceBetween={24}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          600: {
            slidesPerView: 2,
          },
          900: {
            slidesPerView: 2.5,
          },
          1200: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay, Pagination, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        navigation={{
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        }}
        className="specialitySwiper"
        style={{
          "--swiper-navigation-color": theme.palette.primary.main,
          "--swiper-navigation-size": "24px",
          "--swiper-pagination-color": theme.palette.primary.main,
          paddingBottom: "40px",
        }}
      >
        {specialities.map((speciality, index) => {
          const {
            code = null,
            displayName = "",
            shortDescription = "",
            iconUrl = "",
            bannerUrl = "",
            seoSlug = "",
          } = speciality || {};

          return (
            <SwiperSlide key={code || index}>
              <Box
                sx={{
                  background: "#fff",
                  borderRadius: "12px",
                  overflow: "hidden",
                  boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                  cursor: "pointer",
                  transition: "transform 0.3s ease, box-shadow 0.3s ease",
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: "0 8px 30px rgba(0,0,0,0.15)",
                  },
                }}
                onClick={() => handleSpecialityClick(speciality)}
              >
                {/* Image Section */}
                <Box
                  sx={{
                    height: "200px",
                    position: "relative",
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {bannerUrl || iconUrl ? (
                    <Image
                      alt={displayName || "speciality"}
                      src={getThumborUrl(bannerUrl || iconUrl, 350, 200)}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  ) : (
                    <Box
                      sx={{
                        width: "80px",
                        height: "80px",
                        borderRadius: "50%",
                        background: theme.palette.primary.main,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <MedicationIcon 
                        sx={{ 
                          fontSize: "40px", 
                          color: "#fff" 
                        }} 
                      />
                    </Box>
                  )}
                </Box>

                {/* Content Section */}
                <Box
                  sx={{
                    padding: "20px",
                    flex: 1,
                    display: "flex",
                    flexDirection: "column",
                    gap: "12px",
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      fontSize: "18px",
                      color: theme.palette.text.primary,
                      lineHeight: 1.3,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "2",
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {displayName || ""}
                  </Typography>
                  
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.6)",
                      fontSize: "14px",
                      lineHeight: 1.5,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "3",
                      WebkitBoxOrient: "vertical",
                      flex: 1,
                    }}
                  >
                    {shortDescription || "Expert medical care and advanced treatment options available."}
                  </Typography>
                </Box>
              </Box>
            </SwiperSlide>
          );
        })}
      </Swiper>
    </Box>
  );
};

export default Speciality;
