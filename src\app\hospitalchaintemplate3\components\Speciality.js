"use client";

import { Box, Typography, Skeleton, IconButton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import MedicationIcon from "@mui/icons-material/Medication";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { useRef } from "react";

const Speciality = ({ specialities = [], isLoading = false, locationCode }) => {
  const router = useRouter();
  const theme = useTheme();
  const swiperRef = useRef(null);

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "24px", overflow: "hidden" }}>
        {[1, 2, 3].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={300}
            sx={{ 
              width: "100%", 
              minWidth: { xs: "280px", sm: "320px", md: "350px" },
              borderRadius: "12px"
            }}
          />
        ))}
      </Box>
    );
  }

  const handleSpecialityClick = (speciality) => {
    const { seoSlug = "" } = speciality || {};
    if (seoSlug && locationCode) {
      router.push(`/specialities/${locationCode}/${seoSlug}`);
    }
  };

  return (
    <Box>
      <Swiper
        ref={swiperRef}
        spaceBetween={24}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          600: {
            slidesPerView: 2,
          },
          900: {
            slidesPerView: 2.5,
          },
          1200: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        navigation={false}
        className="specialitySwiper"
      >
        {specialities.map((speciality, index) => {
          const {
            code = null,
            displayName = "",
            shortDescription = "",
            iconUrl = "",
            bannerUrl = "",
            seoSlug = "",
          } = speciality || {};

          return (
            <SwiperSlide key={code || index}>
              <Box
                sx={{
                  background: "#fff",
                  borderRadius: "12px",
                  overflow: "hidden",
                  boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                  cursor: "pointer",
                  transition: "transform 0.3s ease, box-shadow 0.3s ease",
                  height: "320px", // Fixed height for all cards
                  display: "flex",
                  flexDirection: "column",
                  "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: "0 8px 30px rgba(0,0,0,0.15)",
                  },
                }}
                onClick={() => handleSpecialityClick(speciality)}
              >
                {/* Image Section */}
                <Box
                  sx={{
                    height: "180px", // Reduced height to fit fixed card height
                    position: "relative",
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {bannerUrl || iconUrl ? (
                    <Image
                      alt={displayName || "speciality"}
                      src={getThumborUrl(bannerUrl || iconUrl, 350, 200)}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  ) : (
                    <Box
                      sx={{
                        width: "80px",
                        height: "80px",
                        borderRadius: "50%",
                        background: theme.palette.primary.main,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <MedicationIcon 
                        sx={{ 
                          fontSize: "40px", 
                          color: "#fff" 
                        }} 
                      />
                    </Box>
                  )}
                </Box>

                {/* Content Section */}
                <Box
                  sx={{
                    padding: "16px",
                    height: "140px", // Fixed height for content section
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      fontSize: "16px",
                      color: theme.palette.text.primary,
                      lineHeight: 1.3,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "2",
                      WebkitBoxOrient: "vertical",
                      marginBottom: "8px",
                    }}
                  >
                    {displayName || ""}
                  </Typography>

                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.6)",
                      fontSize: "13px",
                      lineHeight: 1.4,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      WebkitLineClamp: "2", // Limit to exactly 2 lines
                      WebkitBoxOrient: "vertical",
                      height: "36px", // Fixed height for 2 lines
                    }}
                  >
                    {shortDescription || "Expert medical care and advanced treatment options available."}
                  </Typography>
                </Box>
              </Box>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Custom Navigation Arrows */}
      <Box
        sx={{
          display: "flex",
          gap: "8px",
          marginTop: "20px",
          justifyContent: "flex-start",
        }}
      >
        <IconButton
          onClick={() => swiperRef.current?.swiper?.slidePrev()}
          sx={{
            width: "40px",
            height: "40px",
            backgroundColor: theme.palette.primary.main,
            color: "#fff",
            "&:hover": {
              backgroundColor: theme.palette.primary.dark || theme.palette.primary.main,
            },
          }}
        >
          <ArrowBackIosIcon sx={{ fontSize: "18px", marginLeft: "4px" }} />
        </IconButton>

        <IconButton
          onClick={() => swiperRef.current?.swiper?.slideNext()}
          sx={{
            width: "40px",
            height: "40px",
            backgroundColor: theme.palette.primary.main,
            color: "#fff",
            "&:hover": {
              backgroundColor: theme.palette.primary.dark || theme.palette.primary.main,
            },
          }}
        >
          <ArrowForwardIosIcon sx={{ fontSize: "18px" }} />
        </IconButton>
      </Box>
    </Box>
  );
};

export default Speciality;
