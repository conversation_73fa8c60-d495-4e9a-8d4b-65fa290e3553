"use client";

import { alpha, Box, Typography } from "@mui/material";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { AppContext } from "../../AppContextLayout";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import Speciality from "./Speciality";
import { ArrowRightAlt } from "@mui/icons-material";

const SpecialityWrapper = () => {
  const [specialities, setSpecialities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { websiteData = {} } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, location_code: locationCode = null } =
    websiteData || {};
  const router = useRouter();
  const theme = useTheme();

  const getSpecialities = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        setSpecialities(specialities);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getSpecialities();
  }, [enterpriseCode]);

  if (!specialities.length && !isLoading) {
    return null;
  }

  const handleViewAllClick = () => {
    router.push(`/specialities`);
  };

  return (
    <Box sx={{ padding: { xs: "16px 16px", sm: "24px 16px", md: "32px 80px", lg: "48px 100px" }, backgroundColor: `${alpha(theme.palette.secondary.main, .05)}` }}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
        {/* Custom Header Section */}
        <Box sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexDirection: { xs: "column", sm: "row" }, // Column on mobile, row on larger screens
          gap: { xs: "16px", sm: "0" }, // Add gap on mobile
          alignItems: { xs: "flex-start", sm: "center" }, // Align to start on mobile
        }}>
          <Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                fontSize: { xs: "24px", sm: "28px", md: "32px" },
                color: theme.palette.text.primary,
                marginBottom: "8px",
              }}
            >
              Centre of Excellence
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "rgba(0, 0, 0, 0.6)",
                fontSize: "14px",
                maxWidth: "600px",
                lineHeight: 1.5,
              }}
            >
              From maternity care to pediatric specialties, comprehensive services and advanced care with a compassionate approach to ensure tailored to every age and need.
            </Typography>
          </Box>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.primary.main,
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              fontSize: "16px",
              fontWeight: 500,
              alignSelf: { xs: "flex-start", sm: "auto" }, // Align to start on mobile
            }}
            onClick={handleViewAllClick}
          >
            View All <ArrowRightAlt />
          </Typography>
        </Box>

        <Speciality
          specialities={specialities}
          isLoading={isLoading}
          locationCode={locationCode}
        />
      </Box>
    </Box>
  );
};

export default SpecialityWrapper;
