"use client";

import { Box } from "@mui/material";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { AppContext } from "../../AppContextLayout";
import SectionLayoutChainTemp2 from "../styledComponents/SectionLayoutChainTemp2";
import SectionHeading from "./sectionHeading";
import Speciality from "./Speciality";

const SpecialityWrapper = ({ enterpriseCode, locationCode }) => {
  const [specialities, setSpecialities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { websiteData = {}, setViewSnackbarMain } = useContext(AppContext);

  const getSpecialities = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        console.log("specialities", specialities);
        setSpecialities(specialities);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getSpecialities();
  }, [enterpriseCode]);

  if (!specialities.length && !isLoading) {
    return null;
  }

  return (
    <SectionLayoutChainTemp2>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
        <SectionHeading>Centre of Excellence</SectionHeading>
        <Speciality 
          specialities={specialities} 
          isLoading={isLoading}
          locationCode={locationCode}
        />
      </Box>
    </SectionLayoutChainTemp2>
  );
};

export default SpecialityWrapper;
